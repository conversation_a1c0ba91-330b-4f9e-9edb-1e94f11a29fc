# 缓存目录
cacheFolder: .yarn/berry/cache
# 生成软件缓存的方式 "required-only" | "match-spec" | "always" (default), “仅必需” |“匹配规格” |“总是”
cacheMigrationMode: match-spec
# 缓存行为 "throw" | "update" | "ignore" | "reset",
checksumBehavior: update
# 并发网络请求数量 默认50
networkConcurrency: 50
# 并发下载数量 默认2
cloneConcurrency : 10
# 压缩级别  0 （“无压缩，更快”） 到 9 （“重度压缩，较慢”） "mixed" （“混合压缩”）
compressionLevel : 0
# 默认的版本前缀  "^" （默认值）、 "~" 或 ""
defaultSemverRangePrefix : ^

# 进度条样式 "patrick" | "simba" | "jack" | "hogsfather" | "default",
progressBarStyle: "patrick"
# 依赖下载路径
#   "pnp" , 生成单个 Node.js 加载程序文件
#   "node-modules", 与 npm 一致创建 node-modules 目录,
#   "pnpm" 使用指向全局内容可寻址存储的符号链接和硬链接创建 node-modules 目录
nodeLinker: node-modules
# npm 镜像地址
npmRegistryServer: "https://registry.npmmirror.com/"
# 是否启用镜像源加速，提高包下载速度
enableMirror: true
# 使用硬链接优化本地依赖，提高安装速度
nmMode: hardlinks-local